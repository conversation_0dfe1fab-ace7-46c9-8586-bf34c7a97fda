import {
  Injectable,
  type NestInterceptor,
  type Execution<PERSON>ontext,
  type <PERSON><PERSON><PERSON><PERSON>,
  BadRequestException,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { of, tap, catchError } from 'rxjs';
import { Keyv } from 'keyv';
import KeyvRedis from '@keyv/redis';

import { type RedisConfig } from '@libs';

@Injectable()
export class IdempotencyInterceptor implements NestInterceptor {
  private readonly keyv: Keyv;
  private readonly logger = new Logger(IdempotencyInterceptor.name);

  constructor(readonly config: RedisConfig) {
    const redisStore = new KeyvRedis({
      host: config.host,
      port: config.port,
      password: config.password,
      db: config.db,
    });

    this.keyv = new Keyv({
      store: redisStore,
      namespace: config.keyPrefix,
    });

    this.keyv.on('error', error => {
      this.logger.error('Redis connection error in idempotency interceptor', error);
    });

    this.logger.log(
      `idempotency interceptor initialized with <PERSON>is at ${config.host}:${config.port}`
    );
  }

  async intercept(ctx: ExecutionContext, next: CallHandler) {
    const req = ctx.switchToHttp().getRequest<Request>();
    const key = req.headers.get('Idempotency-Key');
    const appCode = req.headers.get('App-Code')?.toLowerCase() ?? 'default';
    if (!key) throw new BadRequestException('Idempotency-Key header is required');

    const reserveKey = `idemp:${appCode}:${key}`;
    const respKey = `${reserveKey}:resp`;

    const cached = await this.keyv.get(respKey);
    if (cached) return of(JSON.parse(cached));

    const reserved = await this.keyv.set(reserveKey, 'processing', 86400);
    if (!reserved) {
      const maybe = await this.keyv.get(respKey);
      if (maybe) return of(JSON.parse(maybe));
      throw new HttpException({ status: 'processing' }, HttpStatus.ACCEPTED);
    }

    return next.handle().pipe(
      tap(async resp => {
        await this.keyv.set(respKey, JSON.stringify(resp), 86400);
        await this.keyv.set(reserveKey, 'done', 86400);
      }),
      catchError(async err => {
        await this.keyv.delete(reserveKey);
        throw err;
      })
    );
  }
}
