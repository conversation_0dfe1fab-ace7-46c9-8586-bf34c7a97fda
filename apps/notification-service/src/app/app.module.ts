import {
  DatabaseModule,
  HostModule,
  QueueModule,
  RateLimitingModule,
  type RedisConfig,
} from '@libs';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService, registerAs } from '@nestjs/config';
import { AppController } from './app.controller';
import { NotificationsController } from './controllers/notifications.controller';
import { NotificationService } from './services/notification.service';
import { HealthController } from './controllers/health.controller';
import { TerminusModule } from '@nestjs/terminus';
import { AuthModule } from '../infrastructure/auth/auth.module';
import { DeviceService } from './services/device.service';
import { DeviceController } from './controllers/device.controller';
import { UserPreferencesService } from './services/user-preferences.service';
import { UserPreferencesController } from './controllers/user-preferences.controller';
import { IdempotencyInterceptor } from './interceptors/idempotency.interceptor';
import { APP_INTERCEPTOR } from '@nestjs/core';

const redisConfig = registerAs(
  'redis',
  (): RedisConfig => ({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'rate-limit:',
  })
);

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
      load: [redisConfig],
    }),
    QueueModule,
    DatabaseModule,
    TerminusModule,
    HostModule,
    AuthModule,
    RateLimitingModule,
  ],
  controllers: [
    AppController,
    NotificationsController,
    HealthController,
    DeviceController,
    UserPreferencesController,
  ],
  providers: [
    NotificationService,
    DeviceService,
    UserPreferencesService,
    {
      provide: APP_INTERCEPTOR,
      useFactory: (configService: ConfigService) => {
        const redisConfig = configService.get<RedisConfig>('redis');
        console.log('🐞🐞🐞 ~ AppModule ~ redisConfig:', redisConfig);
        return new IdempotencyInterceptor(redisConfig);
      },
      inject: [ConfigService],
    },
  ],
})
export class AppModule {}
