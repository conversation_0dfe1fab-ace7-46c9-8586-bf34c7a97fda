import { Inject, Injectable, Logger } from '@nestjs/common';
import { Keyv } from 'keyv';
import KeyvRedis from '@keyv/redis';
import { type RedisConfig } from '../../shared/configs/redis';

@Injectable()
export class CacheService {
  private readonly keyv: Keyv;
  private readonly logger = new Logger(CacheService.name);

  constructor(@Inject() readonly config: RedisConfig) {
    const redisStore = new KeyvRedis({
      host: config.host,
      port: config.port,
      password: config.password,
      db: config.db,
    });

    this.keyv = new Keyv({
      store: redisStore,
      namespace: config.keyPrefix,
    });

    this.keyv.on('error', error => {
      this.logger.error('Redis connection error in cache service', error);
    });

    this.logger.log(`Cache service initialized with Redis at ${config.host}:${config.port}`);
  }

  async get(key: string): Promise<any> {
    return this.keyv.get(key);
  }

  async set(key: string, value: any, ttl?: number): Promise<void> {
    await this.keyv.set(key, value, ttl);
  }

  async delete(key: string): Promise<void> {
    await this.keyv.delete(key);
  }
}
