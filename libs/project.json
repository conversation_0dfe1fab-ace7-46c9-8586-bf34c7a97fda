{"name": "libs", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs", "tsConfig": "libs/tsconfig.lib.json", "packageJson": "libs/package.json", "main": "libs/src/index.ts", "assets": ["libs/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/**/*.ts"]}}}}